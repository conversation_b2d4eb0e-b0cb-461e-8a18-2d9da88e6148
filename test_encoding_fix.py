#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح مشكلة التشفير (encoding) في ميزة حذف المستخدمين
تاريخ الإنشاء: 2025-07-21
الهدف: التحقق من أن مشكلة UTF-8 codec تم حلها
"""

import re

class TestEncodingFix:
    """اختبار إصلاح مشكلة التشفير"""
    
    def __init__(self):
        self.original_file = "اخر حاجة  - كروت وبوت.py"
        
    def test_safe_decode_function(self):
        """اختبار وجود دالة safe_decode_text"""
        print("🔍 اختبار وجود دالة safe_decode_text...")
        
        with open(self.original_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن الدالة
        func_pattern = r'def safe_decode_text\(self, text\):'
        if not re.search(func_pattern, content):
            print("❌ دالة safe_decode_text غير موجودة")
            return False
        
        # التحقق من محتوى الدالة
        func_match = re.search(r'def safe_decode_text.*?(?=def|\Z)', content, re.DOTALL)
        if not func_match:
            print("❌ لم يتم العثور على محتوى دالة safe_decode_text")
            return False
        
        func_code = func_match.group(0)
        
        # التحقق من العناصر المطلوبة
        required_elements = [
            'isinstance(text, bytes)',
            'text.decode(\'utf-8\')',
            'UnicodeDecodeError',
            'text.decode(\'latin-1\', errors=\'ignore\')',
            'text.decode(\'cp1252\', errors=\'ignore\')'
        ]
        
        for element in required_elements:
            if element not in func_code:
                print(f"❌ العنصر المطلوب غير موجود: {element}")
                return False
        
        print("✅ دالة safe_decode_text موجودة ومكتملة")
        return True
    
    def test_clean_mikrotik_data_function(self):
        """اختبار وجود دالة clean_mikrotik_data"""
        print("\n🔍 اختبار وجود دالة clean_mikrotik_data...")
        
        with open(self.original_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن الدالة
        func_pattern = r'def clean_mikrotik_data\(self, data\):'
        if not re.search(func_pattern, content):
            print("❌ دالة clean_mikrotik_data غير موجودة")
            return False
        
        # التحقق من محتوى الدالة
        func_match = re.search(r'def clean_mikrotik_data.*?(?=def|\Z)', content, re.DOTALL)
        if not func_match:
            print("❌ لم يتم العثور على محتوى دالة clean_mikrotik_data")
            return False
        
        func_code = func_match.group(0)
        
        # التحقق من العناصر المطلوبة
        required_elements = [
            'isinstance(data, dict)',
            'isinstance(data, list)',
            'self.safe_decode_text(value)',
            'self.clean_mikrotik_data(item)'
        ]
        
        for element in required_elements:
            if element not in func_code:
                print(f"❌ العنصر المطلوب غير موجود: {element}")
                return False
        
        print("✅ دالة clean_mikrotik_data موجودة ومكتملة")
        return True
    
    def test_search_function_uses_clean_data(self):
        """اختبار أن دالة البحث تستخدم تنظيف البيانات"""
        print("\n🔍 اختبار استخدام تنظيف البيانات في دالة البحث...")
        
        with open(self.original_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن دالة search_and_confirm_delete_users
        func_match = re.search(r'def search_and_confirm_delete_users.*?(?=def|\Z)', content, re.DOTALL)
        if not func_match:
            print("❌ لم يتم العثور على دالة search_and_confirm_delete_users")
            return False
        
        func_code = func_match.group(0)
        
        # التحقق من استخدام تنظيف البيانات
        if 'self.clean_mikrotik_data(user)' not in func_code:
            print("❌ دالة البحث لا تستخدم clean_mikrotik_data")
            return False
        
        # التحقق من عدم وجود معالجة التشفير اليدوية القديمة
        old_patterns = [
            'isinstance(comment, bytes)',
            'isinstance(email, bytes)',
            'comment.decode(\'utf-8\')',
            'email.decode(\'utf-8\')'
        ]
        
        for pattern in old_patterns:
            if pattern in func_code:
                print(f"⚠️ لا تزال هناك معالجة تشفير يدوية قديمة: {pattern}")
        
        print("✅ دالة البحث تستخدم تنظيف البيانات بشكل صحيح")
        return True
    
    def test_execute_function_uses_clean_data(self):
        """اختبار أن دالة التنفيذ تستخدم تنظيف البيانات"""
        print("\n🔍 اختبار استخدام تنظيف البيانات في دالة التنفيذ...")
        
        with open(self.original_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن دالة execute_delete_users_by_email
        func_match = re.search(r'def execute_delete_users_by_email.*?(?=def|\Z)', content, re.DOTALL)
        if not func_match:
            print("❌ لم يتم العثور على دالة execute_delete_users_by_email")
            return False
        
        func_code = func_match.group(0)
        
        # التحقق من استخدام تنظيف البيانات
        if 'self.clean_mikrotik_data(user)' not in func_code:
            print("❌ دالة التنفيذ لا تستخدم clean_mikrotik_data")
            return False
        
        # التحقق من استخدام safe_decode_text في معالجة الأخطاء
        if 'self.safe_decode_text(user.get(\'name\'' not in func_code:
            print("❌ دالة التنفيذ لا تستخدم safe_decode_text في معالجة الأخطاء")
            return False
        
        print("✅ دالة التنفيذ تستخدم تنظيف البيانات بشكل صحيح")
        return True
    
    def test_encoding_error_handling(self):
        """اختبار معالجة أخطاء التشفير"""
        print("\n🔍 اختبار معالجة أخطاء التشفير...")
        
        with open(self.original_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن معالجة UnicodeDecodeError
        unicode_error_count = len(re.findall(r'UnicodeDecodeError', content))
        
        if unicode_error_count < 2:  # يجب أن تكون في safe_decode_text على الأقل
            print(f"❌ معالجة UnicodeDecodeError غير كافية: {unicode_error_count}")
            return False
        
        # البحث عن استخدام errors='ignore'
        ignore_errors_count = len(re.findall(r'errors=[\'"]ignore[\'"]', content))
        
        if ignore_errors_count < 2:
            print(f"❌ استخدام errors='ignore' غير كافي: {ignore_errors_count}")
            return False
        
        print(f"✅ معالجة أخطاء التشفير موجودة: {unicode_error_count} UnicodeDecodeError, {ignore_errors_count} errors='ignore'")
        return True
    
    def test_multiple_encoding_support(self):
        """اختبار دعم تشفيرات متعددة"""
        print("\n🔍 اختبار دعم تشفيرات متعددة...")
        
        with open(self.original_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن دالة safe_decode_text
        func_match = re.search(r'def safe_decode_text.*?(?=def|\Z)', content, re.DOTALL)
        if not func_match:
            print("❌ لم يتم العثور على دالة safe_decode_text")
            return False
        
        func_code = func_match.group(0)
        
        # التحقق من دعم تشفيرات متعددة
        supported_encodings = [
            'utf-8',
            'latin-1',
            'cp1252'
        ]
        
        for encoding in supported_encodings:
            if encoding not in func_code:
                print(f"❌ التشفير غير مدعوم: {encoding}")
                return False
        
        print(f"✅ دعم تشفيرات متعددة: {', '.join(supported_encodings)}")
        return True
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار إصلاح مشكلة التشفير")
        print("="*60)
        
        tests = [
            ("دالة safe_decode_text", self.test_safe_decode_function),
            ("دالة clean_mikrotik_data", self.test_clean_mikrotik_data_function),
            ("استخدام تنظيف البيانات في البحث", self.test_search_function_uses_clean_data),
            ("استخدام تنظيف البيانات في التنفيذ", self.test_execute_function_uses_clean_data),
            ("معالجة أخطاء التشفير", self.test_encoding_error_handling),
            ("دعم تشفيرات متعددة", self.test_multiple_encoding_support)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                print(f"\n🧪 تشغيل: {test_name}")
                result = test_func()
                if result:
                    print(f"✅ نجح: {test_name}")
                    passed += 1
                else:
                    print(f"❌ فشل: {test_name}")
                    failed += 1
            except Exception as e:
                print(f"❌ خطأ في {test_name}: {str(e)}")
                failed += 1
        
        print("\n" + "="*60)
        print("📊 نتائج الاختبار:")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        
        if failed == 0:
            print("🎉 تم إصلاح مشكلة التشفير بنجاح!")
            print("\n🔥 الإصلاحات المطبقة:")
            print("• ✅ دالة safe_decode_text للمعالجة الآمنة")
            print("• ✅ دالة clean_mikrotik_data لتنظيف البيانات")
            print("• ✅ دعم UTF-8, Latin-1, CP1252")
            print("• ✅ معالجة UnicodeDecodeError")
            print("• ✅ استخدام errors='ignore' للأمان")
            print("\n💡 الميزة جاهزة للاستخدام مع أي تشفير!")
        else:
            print("⚠️ لا تزال هناك مشاكل في إصلاح التشفير.")
        
        return failed == 0

def main():
    """الدالة الرئيسية"""
    tester = TestEncodingFix()
    success = tester.run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
