#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح زر "حذف الكروت الناجحة" للكرت الواحد في نظام HotSpot
تاريخ الإنشاء: 2025-07-21
الهدف: التحقق من إصلاح النص والوظيفة لزر حذف الكروت الناجحة
"""

import re

def test_button_text_correctness():
    """اختبار صحة نص الزر"""
    print("🔍 اختبار صحة نص الزر...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من النص الصحيح للزر
    correct_button_text = '"text": f"🗑️ حذف الكروت الناجحة ({success_count})"'
    if correct_button_text not in content:
        print(f"❌ النص الصحيح للزر غير موجود: {correct_button_text}")
        return False
    print(f"✅ النص الصحيح للزر موجود: {correct_button_text}")
    
    # التحقق من callback_data الصحيح
    correct_callback = '"callback_data": f"single_card_delete_successful_{success_count}"'
    if correct_callback not in content:
        print(f"❌ callback_data الصحيح غير موجود: {correct_callback}")
        return False
    print(f"✅ callback_data الصحيح موجود: {correct_callback}")
    
    return True

def test_callback_handling():
    """اختبار معالجة callback"""
    print("\n🔍 اختبار معالجة callback...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من معالجة callback الأساسية
    callback_handlers = [
        'elif callback_data.startswith("single_card_delete_successful_"):',
        'if callback_data.startswith("single_card_delete_successful_confirm_"):',
        'elif callback_data == "single_card_delete_successful_cancel":',
        'self.handle_single_card_delete_successful_request(bot_token, chat_id, success_count)',
        'self.execute_single_card_delete_successful(bot_token, chat_id, cards_count)',
        'self.cancel_single_card_delete_successful(bot_token, chat_id)'
    ]
    
    for handler in callback_handlers:
        if handler not in content:
            print(f"❌ معالج callback غير موجود: {handler}")
            return False
        print(f"✅ معالج callback موجود: {handler}")
    
    return True

def test_handle_function():
    """اختبار دالة handle_single_card_delete_successful_request"""
    print("\n🔍 اختبار دالة handle_single_card_delete_successful_request...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن الدالة
    func_match = re.search(r'def handle_single_card_delete_successful_request.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة handle_single_card_delete_successful_request")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من العناصر المطلوبة في الدالة
    required_elements = [
        'معالجة طلب حذف الكروت الناجحة للكرت الواحد',
        'if not hasattr(self, \'single_card_successful_cards\')',
        'لا توجد معلومات محفوظة عن الكروت الناجحة',
        'system_type\') != \'hotspot\'',
        'ميزة حذف الكروت الناجحة تعمل فقط مع نظام الهوت سبوت',
        'cards_to_delete = len(self.single_card_successful_cards)',
        'تأكيد حذف الكروت الناجحة',
        'حذف الكروت الناجحة من خادم MikroTik',
        'نعم، احذف الكروت الناجحة',
        'إلغاء - الاحتفاظ بالكروت الناجحة',
        'single_card_delete_successful_confirm_',
        'single_card_delete_successful_cancel'
    ]
    
    for element in required_elements:
        if element not in func_code:
            print(f"❌ عنصر مطلوب غير موجود في الدالة: {element}")
            return False
        print(f"✅ عنصر مطلوب موجود في الدالة: {element}")
    
    return True

def test_execute_function():
    """اختبار دالة execute_single_card_delete_successful"""
    print("\n🔍 اختبار دالة execute_single_card_delete_successful...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن الدالة
    func_match = re.search(r'def execute_single_card_delete_successful.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة execute_single_card_delete_successful")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من العناصر المطلوبة في الدالة
    required_elements = [
        'تنفيذ عملية حذف الكروت الناجحة للكرت الواحد من MikroTik',
        'بدء تنفيذ حذف الكروت الناجحة للكرت الواحد',
        'if not hasattr(self, \'single_card_successful_cards\')',
        'لا توجد معلومات عن الكروت المطلوب حذفها',
        'بدء عملية حذف الكروت الناجحة',
        'جاري حذف {len(self.single_card_successful_cards)} كرت ناجح من خادم MikroTik',
        'api = self.connect_api()',
        'فشل في الاتصال',
        'لا يمكن الاتصال بخادم MikroTik لتنفيذ عملية الحذف',
        'deleted_count = self.delete_successful_cards_from_mikrotik',
        'تم حذف الكروت الناجحة!',
        'إحصائيات الحذف:',
        'إجمالي الكروت المطلوب حذفها:',
        'الكروت المحذوفة بنجاح:',
        'معدل نجاح الحذف:'
    ]
    
    for element in required_elements:
        if element not in func_code:
            print(f"❌ عنصر مطلوب غير موجود في الدالة: {element}")
            return False
        print(f"✅ عنصر مطلوب موجود في الدالة: {element}")
    
    return True

def test_delete_function_fix():
    """اختبار إصلاح دالة delete_successful_cards_from_mikrotik"""
    print("\n🔍 اختبار إصلاح دالة delete_successful_cards_from_mikrotik...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن الدالة
    func_match = re.search(r'def delete_successful_cards_from_mikrotik.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة delete_successful_cards_from_mikrotik")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من الطريقة الصحيحة للحذف
    correct_deletion_method = [
        'البحث عن المستخدم أولاً للحصول على ID',
        'hotspot_users = api.get_resource(\'/ip/hotspot/user\')',
        'users = hotspot_users.get(name=username)',
        'if users:',
        'user_id = users[0][\'.id\']',
        'hotspot_users.remove(user_id)',
        'تم حذف المستخدم: {username} (ID: {user_id})',
        'لم يتم العثور على المستخدم: {username}'
    ]
    
    for method in correct_deletion_method:
        if method not in func_code:
            print(f"❌ طريقة الحذف الصحيحة غير موجودة: {method}")
            return False
        print(f"✅ طريقة الحذف الصحيحة موجودة: {method}")
    
    # التحقق من عدم وجود الطريقة القديمة الخاطئة
    old_wrong_method = 'api.get_resource(\'/ip/hotspot/user\').remove(numbers=username)'
    if old_wrong_method in func_code:
        print(f"❌ الطريقة القديمة الخاطئة ما زالت موجودة: {old_wrong_method}")
        return False
    print("✅ الطريقة القديمة الخاطئة غير موجودة")
    
    return True

def test_cancel_function():
    """اختبار دالة cancel_single_card_delete_successful"""
    print("\n🔍 اختبار دالة cancel_single_card_delete_successful...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن الدالة
    func_match = re.search(r'def cancel_single_card_delete_successful.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة cancel_single_card_delete_successful")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من العناصر المطلوبة في الدالة
    required_elements = [
        'إلغاء عملية حذف الكروت الناجحة للكرت الواحد',
        'تم إلغاء حذف الكروت الناجحة',
        'لم يتم حذف أي كروت',
        'جميع الكروت الناجحة من عملية الكرت الواحد الحالية ما زالت موجودة',
        'يمكنك طلب حذف الكروت الناجحة مرة أخرى'
    ]
    
    for element in required_elements:
        if element not in func_code:
            print(f"❌ عنصر مطلوب غير موجود في الدالة: {element}")
            return False
        print(f"✅ عنصر مطلوب موجود في الدالة: {element}")
    
    return True

def test_logging_messages():
    """اختبار رسائل التسجيل المحدثة"""
    print("\n🔍 اختبار رسائل التسجيل المحدثة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من رسائل التسجيل المحدثة
    updated_logging_messages = [
        'طلب حذف أولي للكروت الناجحة للكرت الواحد',
        'معالجة callback لحذف الكروت الناجحة للكرت الواحد',
        'تأكيد حذف الكروت الناجحة للكرت الواحد',
        'إلغاء حذف الكروت الناجحة للكرت الواحد',
        'بدء معالجة طلب حذف الكروت الناجحة للكرت الواحد',
        'بدء تنفيذ حذف الكروت الناجحة للكرت الواحد',
        'تم تنفيذ حذف الكروت الناجحة للكرت الواحد'
    ]
    
    for message in updated_logging_messages:
        if message not in content:
            print(f"❌ رسالة تسجيل محدثة غير موجودة: {message}")
            return False
        print(f"✅ رسالة تسجيل محدثة موجودة: {message}")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاح زر حذف الكروت الناجحة للكرت الواحد")
    print("="*70)
    
    tests = [
        ("صحة نص الزر", test_button_text_correctness),
        ("معالجة callback", test_callback_handling),
        ("دالة handle_single_card_delete_successful_request", test_handle_function),
        ("دالة execute_single_card_delete_successful", test_execute_function),
        ("إصلاح دالة delete_successful_cards_from_mikrotik", test_delete_function_fix),
        ("دالة cancel_single_card_delete_successful", test_cancel_function),
        ("رسائل التسجيل المحدثة", test_logging_messages)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 تشغيل: {test_name}")
            result = test_func()
            if result:
                print(f"✅ نجح: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل: {test_name}")
                failed += 1
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {str(e)}")
            failed += 1
    
    print("\n" + "="*70)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم إصلاح زر حذف الكروت الناجحة بنجاح!")
        print("💡 الإصلاحات المطبقة:")
        print("✅ تصحيح نص الزر")
        print("✅ إصلاح معالجة callback")
        print("✅ تحسين دالة handle_single_card_delete_successful_request")
        print("✅ تحسين دالة execute_single_card_delete_successful")
        print("✅ إصلاح طريقة حذف المستخدمين من MikroTik")
        print("✅ تحسين دالة cancel_single_card_delete_successful")
        print("✅ تحديث جميع رسائل التسجيل")
        
        print("\n🎯 الآن الزر يعمل بشكل صحيح:")
        print("🔘 النص: 🗑️ حذف الكروت الناجحة (عدد)")
        print("⚙️ الوظيفة: حذف الكروت الناجحة من خادم MikroTik")
        print("🔄 المعالجة: callback يعمل بشكل سليم")
        print("🗑️ الحذف: يستخدم الطريقة الصحيحة (البحث بالاسم ثم الحذف بالID)")
        
    else:
        print("⚠️ بعض المكونات تحتاج إلى مراجعة.")
    
    return failed == 0

if __name__ == "__main__":
    import sys
    sys.exit(0 if main() else 1)
