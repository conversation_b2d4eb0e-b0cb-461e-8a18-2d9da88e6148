#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تغيير مصطلحات "الكروت المرسلة بنجاح" إلى "الكروت الناجحة" للكرت الواحد
تاريخ الإنشاء: 2025-07-21
الهدف: التحقق من تغيير جميع المصطلحات من "المرسلة بنجاح" إلى "الناجحة"
"""

import re

def test_button_text_change():
    """اختبار تغيير نص الزر"""
    print("🔍 اختبار تغيير نص الزر...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من النص الجديد
    new_button_text = 'حذف الكروت الناجحة'
    if new_button_text not in content:
        print(f"❌ النص الجديد للزر غير موجود: {new_button_text}")
        return False
    print(f"✅ النص الجديد للزر موجود: {new_button_text}")
    
    # التحقق من عدم وجود النص القديم في أزرار الكرت الواحد فقط
    # (نستثني أزرار البرق التي تحتوي على lightning_delete_successful)
    single_card_button_contexts = [
        'single_card_delete_successful_',  # للتأكد من أننا نتحقق من أزرار الكرت الواحد فقط
    ]

    # البحث عن أزرار الكرت الواحد التي تحتوي على النص القديم
    lines = content.split('\n')
    found_old_single_card_buttons = False

    for i, line in enumerate(lines):
        # إذا وجدنا سطر يحتوي على single_card_delete_successful_
        if 'single_card_delete_successful_' in line:
            # تحقق من الأسطر المحيطة للبحث عن النص القديم
            context_lines = lines[max(0, i-3):i+4]  # 3 أسطر قبل و 3 بعد
            context_text = '\n'.join(context_lines)

            if 'حذف الكروت المرسلة بنجاح' in context_text and 'single_card' in context_text:
                print(f"❌ النص القديم موجود في أزرار الكرت الواحد في السطر {i+1}")
                found_old_single_card_buttons = True
                break

    if not found_old_single_card_buttons:
        print("✅ لا يوجد نص قديم في أزرار الكرت الواحد")
    
    return not found_old_single_card_buttons

def test_message_descriptions():
    """اختبار تغيير أوصاف الرسائل"""
    print("\n🔍 اختبار تغيير أوصاف الرسائل...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من النصوص الجديدة
    new_descriptions = [
        'حذف الكروت الناجحة:',
        'يمكنك اختيار حذف الـ {success_count} كرت الناجح المرسل إلى خادم MikroTik',
        'تم إضافة زر حذف الكروت الناجحة للكرت الواحد'
    ]
    
    for desc in new_descriptions:
        if desc not in content:
            print(f"❌ الوصف الجديد غير موجود: {desc}")
            return False
        print(f"✅ الوصف الجديد موجود: {desc}")
    
    return True

def test_confirmation_messages():
    """اختبار رسائل التأكيد"""
    print("\n🔍 اختبار رسائل التأكيد...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من رسائل التأكيد الجديدة
    new_confirmation_texts = [
        'تأكيد حذف الكروت الناجحة',
        'حذف الكروت الناجحة من خادم MikroTik',
        'سيتم حذف الكروت الناجحة من عملية الكرت الواحد الحالية فقط',
        'حذف الكروت الناجحة من هذه العملية فقط',
        'نعم، احذف الكروت الناجحة',
        'إلغاء - الاحتفاظ بالكروت الناجحة'
    ]
    
    for text in new_confirmation_texts:
        if text not in content:
            print(f"❌ نص التأكيد الجديد غير موجود: {text}")
            return False
        print(f"✅ نص التأكيد الجديد موجود: {text}")
    
    return True

def test_execution_messages():
    """اختبار رسائل التنفيذ"""
    print("\n🔍 اختبار رسائل التنفيذ...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من رسائل التنفيذ الجديدة
    new_execution_texts = [
        'تنفيذ عملية حذف الكروت الناجحة للكرت الواحد من MikroTik',
        'بدء تنفيذ حذف الكروت الناجحة للكرت الواحد',
        'بدء عملية حذف الكروت الناجحة',
        'جاري حذف {len(self.single_card_successful_cards)} كرت ناجح من خادم MikroTik',
        'تم حذف الكروت الناجحة!',
        'حذف الكروت الناجحة من عملية الكرت الواحد',
        'تم حذف الكروت الناجحة من عملية الكرت الواحد الحالية من خادم MikroTik'
    ]
    
    for text in new_execution_texts:
        if text not in content:
            print(f"❌ نص التنفيذ الجديد غير موجود: {text}")
            return False
        print(f"✅ نص التنفيذ الجديد موجود: {text}")
    
    return True

def test_logging_messages():
    """اختبار رسائل التسجيل"""
    print("\n🔍 اختبار رسائل التسجيل...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من رسائل التسجيل الجديدة
    new_logging_texts = [
        'بدء معالجة طلب حذف الكروت الناجحة للكرت الواحد',
        'لا توجد معلومات محفوظة عن الكروت الناجحة',
        'ميزة حذف الكروت الناجحة تعمل فقط مع نظام الهوت سبوت',
        'تم تنفيذ حذف الكروت الناجحة للكرت الواحد',
        'خطأ في تنفيذ حذف الكروت الناجحة للكرت الواحد',
        'حدث خطأ في تنفيذ حذف الكروت الناجحة'
    ]
    
    for text in new_logging_texts:
        if text not in content:
            print(f"❌ نص التسجيل الجديد غير موجود: {text}")
            return False
        print(f"✅ نص التسجيل الجديد موجود: {text}")
    
    return True

def test_cancellation_messages():
    """اختبار رسائل الإلغاء"""
    print("\n🔍 اختبار رسائل الإلغاء...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من رسائل الإلغاء الجديدة
    new_cancellation_texts = [
        'إلغاء عملية حذف الكروت الناجحة للكرت الواحد',
        'تم إلغاء حذف الكروت الناجحة',
        'جميع الكروت الناجحة من عملية الكرت الواحد الحالية ما زالت موجودة',
        'يمكنك طلب حذف الكروت الناجحة مرة أخرى',
        'خطأ في إلغاء حذف الكروت الناجحة للكرت الواحد'
    ]
    
    for text in new_cancellation_texts:
        if text not in content:
            print(f"❌ نص الإلغاء الجديد غير موجود: {text}")
            return False
        print(f"✅ نص الإلغاء الجديد موجود: {text}")
    
    return True

def test_old_terminology_removed():
    """اختبار إزالة المصطلحات القديمة الخاصة بالكرت الواحد فقط"""
    print("\n🔍 اختبار إزالة المصطلحات القديمة الخاصة بالكرت الواحد...")

    main_file = "اخر حاجة  - كروت وبوت.py"

    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # البحث عن المصطلحات القديمة الخاصة بالكرت الواحد فقط
    # (نستثني مصطلحات البرق)
    lines = content.split('\n')
    found_old_single_card_terms = []

    # البحث عن المصطلحات القديمة في سياق الكرت الواحد
    for i, line in enumerate(lines):
        # إذا وجدنا سطر يحتوي على مصطلحات الكرت الواحد
        if 'single_card' in line.lower() or 'للكرت الواحد' in line:
            # تحقق من وجود المصطلحات القديمة في هذا السطر
            old_terms_in_single_card = [
                'حذف الكروت المرسلة بنجاح',
                'تأكيد حذف الكروت المرسلة بنجاح',
                'تم حذف الكروت المرسلة بنجاح',
                'تم إلغاء حذف الكروت المرسلة بنجاح'
            ]

            for term in old_terms_in_single_card:
                if term in line:
                    found_old_single_card_terms.append(f"السطر {i+1}: {term}")

    if found_old_single_card_terms:
        print("❌ تم العثور على مصطلحات قديمة في سياق الكرت الواحد:")
        for term in found_old_single_card_terms:
            print(f"   - {term}")
        return False
    else:
        print("✅ لم يتم العثور على أي مصطلحات قديمة في سياق الكرت الواحد")
        print("💡 مصطلحات البرق (Lightning) تبقى كما هي وهذا صحيح")
        return True

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار تغيير مصطلحات الكروت الناجحة للكرت الواحد")
    print("="*70)
    
    tests = [
        ("تغيير نص الزر", test_button_text_change),
        ("تغيير أوصاف الرسائل", test_message_descriptions),
        ("رسائل التأكيد", test_confirmation_messages),
        ("رسائل التنفيذ", test_execution_messages),
        ("رسائل التسجيل", test_logging_messages),
        ("رسائل الإلغاء", test_cancellation_messages),
        ("إزالة المصطلحات القديمة", test_old_terminology_removed)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 تشغيل: {test_name}")
            result = test_func()
            if result:
                print(f"✅ نجح: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل: {test_name}")
                failed += 1
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {str(e)}")
            failed += 1
    
    print("\n" + "="*70)
    print("📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 تم تغيير مصطلحات الكروت الناجحة بنجاح!")
        print("💡 التغييرات المطبقة:")
        print("✅ تغيير نص الزر من 'حذف الكروت المرسلة بنجاح' إلى 'حذف الكروت الناجحة'")
        print("✅ تحديث جميع أوصاف الرسائل")
        print("✅ تحديث رسائل التأكيد")
        print("✅ تحديث رسائل التنفيذ")
        print("✅ تحديث رسائل التسجيل")
        print("✅ تحديث رسائل الإلغاء")
        print("✅ إزالة جميع المصطلحات القديمة")
        
        print("\n🎯 النتيجة:")
        print("الآن جميع الرسائل تستخدم مصطلح 'الكروت الناجحة' بدلاً من 'الكروت المرسلة بنجاح'")
        print("الوظيفة تبقى كما هي: حذف الكروت الناجحة من خادم MikroTik")
        
    else:
        print("⚠️ بعض المكونات تحتاج إلى مراجعة.")
    
    return failed == 0

if __name__ == "__main__":
    import sys
    sys.exit(0 if main() else 1)
